{"version": 3, "names": ["_utils", "require", "defineType", "visitor", "aliases", "fields", "process", "env", "BABEL_TYPES_8_BREAKING", "object", "validate", "Object", "assign", "oneOfNodeTypes", "callee", "assertNodeType", "expression", "builder", "body", "async", "assertValueType", "default", "exported", "properties", "validateArrayOfType", "elements", "arrayOfType", "value"], "sources": ["../../src/definitions/experimental.ts"], "sourcesContent": ["import defineType, {\n  arrayOfType,\n  assertNodeType,\n  assertValueType,\n  validateArrayOfType,\n} from \"./utils.ts\";\n\ndefineType(\"ArgumentPlaceholder\", {});\n\ndefineType(\"BindExpression\", {\n  visitor: [\"object\", \"callee\"],\n  aliases: [\"Expression\"],\n  fields:\n    !process.env.BABEL_8_BREAKING && !process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          object: {\n            validate: Object.assign(() => {}, {\n              oneOfNodeTypes: [\"Expression\"],\n            }),\n          },\n          callee: {\n            validate: Object.assign(() => {}, {\n              oneOfNodeTypes: [\"Expression\"],\n            }),\n          },\n        }\n      : {\n          object: {\n            validate: assertNodeType(\"Expression\"),\n          },\n          callee: {\n            validate: assertNodeType(\"Expression\"),\n          },\n        },\n});\n\ndefineType(\"Decorator\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"DoExpression\", {\n  visitor: [\"body\"],\n  builder: [\"body\", \"async\"],\n  aliases: [\"Expression\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    async: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ExportDefaultSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"RecordExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: validateArrayOfType(\"ObjectProperty\", \"SpreadElement\"),\n  },\n});\n\ndefineType(\"TupleExpression\", {\n  fields: {\n    elements: {\n      validate: arrayOfType(\"Expression\", \"SpreadElement\"),\n      default: [],\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  defineType(\"DecimalLiteral\", {\n    builder: [\"value\"],\n    fields: {\n      value: {\n        validate: assertValueType(\"string\"),\n      },\n    },\n    aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n  });\n}\n\n// https://github.com/tc39/proposal-js-module-blocks\ndefineType(\"ModuleExpression\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"Program\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-hack-pipes\ndefineType(\"TopicReference\", {\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-smart-pipes\ndefineType(\"PipelineTopicExpression\", {\n  builder: [\"expression\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelineBareFunction\", {\n  builder: [\"callee\"],\n  visitor: [\"callee\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelinePrimaryTopicReference\", {\n  aliases: [\"Expression\"],\n});\n"], "mappings": ";;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAOA,IAAAC,cAAU,EAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;AAErC,IAAAA,cAAU,EAAC,gBAAgB,EAAE;EAC3BC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAC6B,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAChE;IACEC,MAAM,EAAE;MACNC,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAChCC,cAAc,EAAE,CAAC,YAAY;MAC/B,CAAC;IACH,CAAC;IACDC,MAAM,EAAE;MACNJ,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAChCC,cAAc,EAAE,CAAC,YAAY;MAC/B,CAAC;IACH;EACF,CAAC,GACD;IACEJ,MAAM,EAAE;MACNC,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDD,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACR,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,WAAW,EAAE;EACtBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBE,MAAM,EAAE;IACNW,UAAU,EAAE;MACVN,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,cAAc,EAAE;EACzBC,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBc,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1Bb,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNa,IAAI,EAAE;MACJR,QAAQ,EAAE,IAAAK,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDI,KAAK,EAAE;MACLT,QAAQ,EAAE,IAAAU,sBAAe,EAAC,SAAS,CAAC;MACpCC,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEF,IAAAnB,cAAU,EAAC,wBAAwB,EAAE;EACnCC,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BC,MAAM,EAAE;IACNiB,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,kBAAkB,EAAE;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNkB,UAAU,EAAE,IAAAC,0BAAmB,EAAC,gBAAgB,EAAE,eAAe;EACnE;AACF,CAAC,CAAC;AAEF,IAAAtB,cAAU,EAAC,iBAAiB,EAAE;EAC5BG,MAAM,EAAE;IACNoB,QAAQ,EAAE;MACRf,QAAQ,EAAE,IAAAgB,kBAAW,EAAC,YAAY,EAAE,eAAe,CAAC;MACpDL,OAAO,EAAE;IACX;EACF,CAAC;EACDlB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEiC;EACjC,IAAAF,cAAU,EAAC,gBAAgB,EAAE;IAC3Be,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBZ,MAAM,EAAE;MACNsB,KAAK,EAAE;QACLjB,QAAQ,EAAE,IAAAU,sBAAe,EAAC,QAAQ;MACpC;IACF,CAAC;IACDhB,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;EAC3D,CAAC,CAAC;AACJ;AAGA,IAAAF,cAAU,EAAC,kBAAkB,EAAE;EAC7BC,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBE,MAAM,EAAE;IACNa,IAAI,EAAE;MACJR,QAAQ,EAAE,IAAAK,qBAAc,EAAC,SAAS;IACpC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAIF,IAAAF,cAAU,EAAC,gBAAgB,EAAE;EAC3BE,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAIF,IAAAF,cAAU,EAAC,yBAAyB,EAAE;EACpCe,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBd,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBE,MAAM,EAAE;IACNW,UAAU,EAAE;MACVN,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEF,IAAAF,cAAU,EAAC,sBAAsB,EAAE;EACjCe,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBd,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBE,MAAM,EAAE;IACNS,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEF,IAAAF,cAAU,EAAC,+BAA+B,EAAE;EAC1CE,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC", "ignoreList": []}